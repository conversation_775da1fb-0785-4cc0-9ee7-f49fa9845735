#include "Hardware.h"

uint8_t read_data_command[8] = {0xff, 0x03, 0x60, 0x01, 0x00, 0x01, 0xde, 0x14};

//uint16_t temp;

int main(void)
{	
	hardware_initializes();
	software_initializes();
	
	while(1)
	{
		if(timer2_management.flag_100ms)
		{
			timer2_management.flag_100ms = 0;
			
		}
		/*每次通CAN发送数据前100ms都会向二氧化碳传感器发送读取命令*/
		if(timer2_management.flag_900ms)
		{
			timer2_management.flag_900ms = 0;
			
			/*由于只需要发送一个命令，直接将crc校验值计算出来，放入命令数组中*/
//			temp = Crc16(read_data_command, 6);
//			read_data_command[6] = temp;
//			read_data_command[7] = temp >> 8;
			
			usart2_send_data(read_data_command, 8);//发送读取二氧化碳浓度命令
		}
		/*每个一秒通过CAN发送数据*/
		if(timer2_management.flag_1s)
		{
			timer2_management.flag_1s = 0;
			
			CAN_SendData(0x20A, data_management.concentration, 8);
			LED_turn();
			
		}
	}
}



